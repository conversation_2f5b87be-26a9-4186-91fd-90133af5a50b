# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 🚀 Quick Commands

```bash
cd tinderop                               # Navigate to the main app directory
npm run dev                               # Start development server (Vite)
npm run build                             # Build for production
npm run preview                           # Preview production build
npm run typecheck                         # Run TypeScript type checking
```

## 🛠️ Tech Stack

- **Frontend Framework**: React 19 with TypeScript
- **Build Tool**: Vite 6.0
- **Routing**: React Router v7
- **Styling**: Tailwind CSS with custom design tokens
- **UI Components**: Radix UI primitives with custom styling
- **AI Integration**: Vercel AI SDK with OpenAI
- **Forms**: React Hook Form with Zod validation
- **Icons**: Lucide React
- **Animations**: Framer Motion
- **Development**: Strict TypeScript, ESLint configuration

## 📁 Project Architecture

The project follows a clean React SPA structure in the `tinderop/` directory:

```
tinderop/
├── src/
│   ├── components/           # Reusable UI components
│   │   ├── ui/              # Radix-based design system components
│   │   ├── Layout.tsx       # Root layout wrapper
│   │   └── *-mockup.tsx     # Phone mockup components
│   ├── pages/               # Route-level page components
│   │   ├── LandingPage.tsx  # Homepage with hero, features
│   │   ├── ImageAnalyzer.tsx # Photo analysis tool
│   │   └── BioAnalyzer.tsx  # Bio optimization tool
│   ├── hooks/               # Custom React hooks
│   ├── lib/                 # Utility functions
│   ├── routes.tsx           # React Router configuration
│   ├── main.tsx             # App entry point
│   └── globals.css          # Global styles & CSS variables
├── public/                  # Static assets
└── configuration files
```

## 🎨 Design System

### Color Palette
The app uses a Tinder-inspired color scheme with custom design tokens:
- **Primary**: Flame Red (`#FF5851`) with gradient variants
- **Secondary**: Sparks Pink (`#FF8A80`)
- **Neutral**: Graphite shades (`#1A1A1A`, `#666666`)
- **Background**: Cloud White (`#FFFFFF`)

### Typography Scale
Custom font sizes with consistent line heights:
- `display-1`: 56px/64px (40px/48px mobile)
- `h1`: 40px/48px (32px/40px mobile)  
- `h2`: 32px/40px (24px/32px mobile)
- `body-lg`: 18px/28px
- `body-md`: 16px/24px
- `caption`: 14px/20px

### Component Architecture
- All UI components extend Radix UI primitives
- Consistent use of `class-variance-authority` for component variants
- Tailwind utilities with custom design tokens
- `@/` path alias configured for clean imports

## 🧩 Key Patterns

### Routing Structure
- Uses React Router v7 with nested routes
- Layout component wraps all pages via `<Outlet />`
- Route configuration centralized in `src/routes.tsx`

### Component Composition
```typescript
// Standard component pattern
export function ComponentName() {
  return (
    <div className="custom-styles">
      {/* JSX content */}
    </div>
  );
}
```

### AI Integration
- Uses Vercel AI SDK (`ai` package) for AI features
- OpenAI integration via `@ai-sdk/openai`
- Likely streaming responses for real-time analysis

### State Management
- Primarily uses React hooks (useState, useEffect)
- React Hook Form for complex form handling
- SWR likely used for data fetching (imported in package.json)

## 🔧 Development Workflow

### File Organization
- Components are co-located by feature/page when possible
- Shared UI components in `components/ui/`
- Utilities in `lib/utils.ts`
- Global styles use CSS custom properties for theming

### TypeScript Configuration
- Strict mode enabled with additional checks
- Path aliases: `@/*` maps to `src/*`
- ES2020 target with modern module resolution

### Build & Deployment
- Vite handles bundling and HMR
- PostCSS processes Tailwind CSS
- Production builds optimized for SPA deployment

## 🎯 Application Context

TinderOP is a dating profile optimization tool that provides:
1. **Image Analysis**: AI-powered photo scoring and recommendations
2. **Bio Enhancement**: Bio writing assistance and optimization
3. **Match Insights**: Profile performance analytics

The app focuses on improving dating success through AI-driven recommendations and has a clean, modern interface that mirrors dating app aesthetics.