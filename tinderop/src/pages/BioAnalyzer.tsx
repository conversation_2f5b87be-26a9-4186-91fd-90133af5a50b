import { useState, useCallback } from "react"
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>2, <PERSON><PERSON><PERSON><PERSON><PERSON>, Thum<PERSON>Up } from "lucide-react"
import { <PERSON> } from "react-router"
import { Button } from "@/components/ui/button"
import { Slider } from "@/components/ui/slider"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/hooks/use-toast"
import { Toaster } from "@/components/ui/toaster"
import { generateText } from "ai"
import { openai } from "@ai-sdk/openai"

const DiffViewer = ({ original, rewritten }: { original: string; rewritten: string }) => {
  const originalWords = original.split(/(\s+)/)

  return (
    <p className="text-body-md whitespace-pre-wrap">
      {rewritten.split(/(\s+)/).map((word, i) => {
        if (originalWords.includes(word)) {
          return <span key={i}>{word}</span>
        } else {
          return (
            <span key={i} className="bg-success-green/20 text-success-green rounded px-1">
              {word}
            </span>
          )
        }
      })}
    </p>
  )
}

export function BioAnalyzer() {
  const [bio, setBio] = useState("")
  const [tone, setTone] = useState(1)
  const [rewrittenBio, setRewrittenBio] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  const tones = ["Witty", "Sincere", "Adventurous"]

  const handleCopy = () => {
    if (rewrittenBio) {
      navigator.clipboard.writeText(rewrittenBio)
      toast({
        title: "Copied to clipboard!",
        description: "The rewritten bio is now ready to paste.",
      })
    }
  }

  const rewriteBio = useCallback(async () => {
    if (bio.length < 20) {
      setError("Bio must be at least 20 characters.")
      return
    }
    if (bio.length > 500) {
      setError("Bio must be 500 characters or less.")
      return
    }

    setIsLoading(true)
    setError(null)
    
    try {
      const { text } = await generateText({
        model: openai("gpt-4o-mini"),
        system:
          "You are an expert dating profile writer. Your goal is to rewrite a user's bio to make it more engaging, attractive, and likely to get matches on an app like Tinder. You must adhere to the tone requested by the user. Keep the bio concise, under 75 words.",
        prompt: `Rewrite the following bio with a ${tones[tone]} tone:\n\n"${bio}"`,
      })

      setRewrittenBio(text)
    } catch (error) {
      console.error(error)
      setError("Failed to generate bio. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }, [bio, tone, tones])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    rewriteBio()
  }

  return (
    <>
      <Toaster />
      <div className="min-h-screen bg-gray-50">
        <header className="bg-cloud-white shadow-sm sticky top-0 z-10">
          <div className="container mx-auto px-4 md:px-6 h-16 flex items-center">
            <Link to="/image-analyzer" className="flex items-center gap-2 text-graphite-60 hover:text-graphite-90">
              <ArrowLeft className="h-5 w-5" />
              <span className="font-semibold">Back to Images</span>
            </Link>
          </div>
        </header>

        <main className="container mx-auto px-4 md:px-6 py-8">
          <div className="max-w-2xl mx-auto">
            <h1 className="text-h2-mobile md:text-h2 text-center">Bio Analyzer</h1>
            <p className="text-body-lg text-graphite-60 text-center mt-2">Craft a bio that gets you more matches.</p>

            <form onSubmit={handleSubmit} className="mt-8 space-y-6">
              <div>
                <Textarea
                  placeholder="Paste your current bio here..."
                  value={bio}
                  onChange={(e) => setBio(e.target.value)}
                  className="min-h-[160px] md:min-h-[200px] bg-white"
                  maxLength={500}
                  required
                />
                <p className="text-right text-caption text-graphite-60 mt-1">{bio.length} / 500</p>
              </div>

              <div>
                <label className="font-semibold text-body-md">Select a Tone</label>
                <div className="mt-4">
                  <Slider
                    value={[tone]}
                    onValueChange={(value) => setTone(value[0])}
                    max={2}
                    step={1}
                    className="w-full"
                  />
                  <div className="flex justify-between text-caption text-graphite-60 mt-2">
                    {tones.map((t) => (
                      <span key={t}>{t}</span>
                    ))}
                  </div>
                </div>
              </div>

              <Button type="submit" size="lg" disabled={bio.length < 20 || isLoading} className="w-full">
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                    Generating...
                  </>
                ) : (
                  "Generate New Bio"
                )}
              </Button>
            </form>

            {error && <p className="mt-4 text-center text-error-crimson">{error}</p>}

            {rewrittenBio && (
              <div className="mt-10">
                <Tabs defaultValue="rewrite">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="rewrite">AI Rewrite</TabsTrigger>
                    <TabsTrigger value="original">Original</TabsTrigger>
                    <TabsTrigger value="history" disabled>
                      History
                    </TabsTrigger>
                  </TabsList>
                  <TabsContent value="rewrite" className="mt-4 p-6 bg-white rounded-lg border">
                    <DiffViewer original={bio} rewritten={rewrittenBio} />
                  </TabsContent>
                  <TabsContent value="original" className="mt-4 p-6 bg-white rounded-lg border">
                    <p className="text-body-md whitespace-pre-wrap">{bio}</p>
                  </TabsContent>
                </Tabs>
                <div className="mt-4 flex items-center justify-between">
                  <div className="flex gap-2">
                    <Button onClick={handleCopy} size="sm">
                      <Copy className="mr-2 h-4 w-4" /> Copy
                    </Button>
                    <Button onClick={rewriteBio} variant="secondary" size="sm" disabled={isLoading}>
                      Try Another Tone
                    </Button>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="text-success-green hover:bg-success-green/10 hover:text-success-green"
                    >
                      <ThumbsUp className="h-5 w-5" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="text-error-crimson hover:bg-error-crimson/10 hover:text-error-crimson"
                    >
                      <ThumbsDown className="h-5 w-5" />
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </main>
      </div>
    </>
  )
}