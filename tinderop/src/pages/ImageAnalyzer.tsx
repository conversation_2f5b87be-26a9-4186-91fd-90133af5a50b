import { useState, useC<PERSON>back, useEffect } from "react"
import { ArrowLeft, FileUp, Loader2, Sparkles, X, Shield, Database } from "lucide-react"
import { Link } from "react-router"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { useDropzone } from "react-dropzone"
import { imageStorage, sessionManager, createImagePreview, revokeImagePreview, initStorage, StoredImage } from "@/lib/storage"
import { analysisService } from "@/lib/analysis-service"
import { AnalysisResult, AnalysisProgress, StepResult, ANALYSIS_STEPS } from "@/types/analysis"

type ImageWithPreview = {
  id: string
  fileName: string
  preview: string
  storedImage: StoredImage
}

const ScoreCard = ({ result }: { result: AnalysisResult }) => {
  const getScoreColor = (score: number) => {
    if (score >= 80) return "bg-gradient-primary"
    if (score >= 50) return "bg-warning-amber"
    return "bg-error-crimson"
  }

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden animate-fade-in">
      <img src={result.preview || "/placeholder.svg"} alt={result.fileName} className="w-full h-48 object-cover" />
      <div className="p-4">
        <div className="flex justify-between items-center mb-3">
          <h3 className="font-semibold text-graphite-90 truncate pr-2">{result.fileName}</h3>
          <div className={`px-3 py-1 text-sm font-bold text-white rounded-full ${getScoreColor(result.overallScore)}`}>
            {result.overallScore}
          </div>
        </div>
        
        {/* Step Scores */}
        <div className="mb-4 space-y-1">
          {result.steps.map((step) => (
            <div key={step.stepId} className="flex justify-between items-center text-xs">
              <span className="text-graphite-60">{step.stepName}</span>
              <span className={`font-medium ${step.score >= 70 ? 'text-success-green' : step.score >= 50 ? 'text-warning-amber' : 'text-error-crimson'}`}>
                {step.score}
              </span>
            </div>
          ))}
        </div>
        
        {/* Top Recommendations */}
        <ul className="space-y-2">
          {result.recommendations.slice(0, 3).map((rec, i) => (
            <li key={i} className="flex items-start text-sm text-graphite-60">
              <Sparkles className="h-4 w-4 mr-2 mt-0.5 text-flame-red flex-shrink-0" />
              <span>{rec}</span>
            </li>
          ))}
        </ul>
        
        {result.error && (
          <div className="mt-3 p-2 bg-error-crimson/10 rounded text-sm text-error-crimson">
            {result.error}
          </div>
        )}
      </div>
    </div>
  )
}

const AnalyzingCard = ({ image, progress }: { image: ImageWithPreview; progress?: AnalysisProgress }) => (
  <div className="bg-white rounded-lg shadow-md overflow-hidden">
    <img src={image.preview} alt={image.fileName} className="w-full h-48 object-cover" />
    <div className="p-4">
      <div className="h-6 w-3/4 rounded bg-gray-200 animate-pulse mb-4"></div>
      
      {progress && (
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-graphite-60">Step {progress.currentStep}/5</span>
            <span className="text-flame-red font-medium">{Math.round(progress.progress)}%</span>
          </div>
          <div className="text-sm text-graphite-90 font-medium">{progress.stepName}</div>
          <Progress value={progress.progress} className="h-2" />
        </div>
      )}
      
      <div className="mt-4 space-y-2">
        {ANALYSIS_STEPS.map((step, i) => (
          <div key={step.id} className={`flex items-center text-sm ${
            progress && progress.currentStep > step.id 
              ? 'text-success-green' 
              : progress && progress.currentStep === step.id 
                ? 'text-flame-red' 
                : 'text-gray-400'
          }`}>
            <div className={`w-2 h-2 rounded-full mr-2 ${
              progress && progress.currentStep > step.id 
                ? 'bg-success-green' 
                : progress && progress.currentStep === step.id 
                  ? 'bg-flame-red animate-pulse' 
                  : 'bg-gray-300'
            }`} />
            <span>{step.name}</span>
          </div>
        ))}
      </div>
    </div>
  </div>
)

export function ImageAnalyzer() {
  const [images, setImages] = useState<ImageWithPreview[]>([])
  const [results, setResults] = useState<AnalysisResult[]>([])
  const [status, setStatus] = useState<"idle" | "processing" | "done">("idle")
  const [progress, setProgress] = useState<Record<string, AnalysisProgress>>({})
  const [isStorageReady, setIsStorageReady] = useState(false)
  
  useEffect(() => {
    initStorage().then(() => {
      setIsStorageReady(true)
      // Load any existing images from current session
      loadSessionImages()
    })
    
    return () => {
      // Cleanup previews on unmount
      images.forEach(img => revokeImagePreview(img.preview))
    }
  }, [])
  
  const loadSessionImages = async () => {
    try {
      const session = sessionManager.getCurrentSession()
      const storedImages = await imageStorage.getSessionImages(session.id)
      
      const imageList: ImageWithPreview[] = storedImages.map(stored => ({
        id: stored.id,
        fileName: stored.fileName,
        preview: createImagePreview(stored.blob),
        storedImage: stored
      }))
      
      setImages(imageList)
      
      // Load any existing results
      const existingResults: AnalysisResult[] = []
      for (const img of imageList) {
        const result = sessionManager.getAnalysisResult(img.id)
        if (result) {
          existingResults.push(result)
        }
      }
      
      if (existingResults.length > 0) {
        setResults(existingResults)
        setStatus('done')
      }
    } catch (error) {
      console.error('Failed to load session images:', error)
    }
  }

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (!isStorageReady) return
    
    try {
      const session = sessionManager.getCurrentSession()
      const newImages: ImageWithPreview[] = []
      
      for (const file of acceptedFiles.slice(0, 10 - images.length)) {
        const imageId = await imageStorage.storeImage(file, session.id)
        sessionManager.addImageToSession(imageId)
        
        const storedImage = await imageStorage.getImage(imageId)
        if (storedImage) {
          newImages.push({
            id: imageId,
            fileName: file.name,
            preview: createImagePreview(storedImage.blob),
            storedImage
          })
        }
      }
      
      setImages(prev => [...prev, ...newImages])
    } catch (error) {
      console.error('Failed to store images:', error)
    }
  }, [images.length, isStorageReady])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: { "image/*": [] },
    maxFiles: 10 - images.length,
    disabled: !isStorageReady || images.length >= 10
  })

  const removeImage = async (imageId: string) => {
    try {
      const imageToRemove = images.find(img => img.id === imageId)
      if (imageToRemove) {
        revokeImagePreview(imageToRemove.preview)
        await imageStorage.deleteImage(imageId)
        sessionManager.removeImageFromSession(imageId)
        setImages(prev => prev.filter(img => img.id !== imageId))
        setResults(prev => prev.filter(result => result.fileName !== imageToRemove.fileName))
      }
    } catch (error) {
      console.error('Failed to remove image:', error)
    }
  }

  const handleAnalyze = async () => {
    if (images.length === 0) return
    
    setStatus("processing")
    setResults([])
    setProgress({})
    
    const newResults: AnalysisResult[] = []
    
    try {
      for (const image of images) {
        const result = await analysisService.analyzeImage(image.storedImage, {
          onProgress: (progressData) => {
            setProgress(prev => ({
              ...prev,
              [image.id]: progressData
            }))
          },
          onComplete: (analysisResult) => {
            sessionManager.saveAnalysisResult(image.id, analysisResult)
            newResults.push(analysisResult)
            setResults([...newResults])
          },
          onError: (error) => {
            console.error(`Analysis failed for ${image.fileName}:`, error)
          }
        })
        
        // Small delay between analyses
        if (images.indexOf(image) < images.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000))
        }
      }
      
      setStatus("done")
    } catch (error) {
      console.error('Analysis failed:', error)
      setStatus("idle")
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-cloud-white shadow-sm sticky top-0 z-10">
        <div className="container mx-auto px-4 md:px-6 h-16 flex items-center">
          <Link to="/" className="flex items-center gap-2 text-graphite-60 hover:text-graphite-90">
            <ArrowLeft className="h-5 w-5" />
            <span className="font-semibold">Back to Home</span>
          </Link>
        </div>
        {status === "processing" && (
          <div className="h-1 bg-gray-200">
            <div 
              className="h-full bg-gradient-primary transition-all duration-300" 
              style={{ 
                width: `${Object.values(progress).reduce((avg, p) => avg + p.progress, 0) / Math.max(Object.keys(progress).length, 1)}%` 
              }} 
            />
          </div>
        )}
      </header>

      <main className="container mx-auto px-4 md:px-6 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center">
            <h1 className="text-h2-mobile md:text-h2">Image Analyzer</h1>
            <p className="text-body-lg text-graphite-60 mt-2">
              Upload your photos to get AI-powered feedback.
            </p>
            
            <div className="flex justify-center gap-2 mt-4">
              <Badge variant="secondary" className="flex items-center gap-1">
                <Shield className="h-3 w-3" />
                Images not stored on servers
              </Badge>
              <Badge variant="outline" className="flex items-center gap-1">
                <Database className="h-3 w-3" />
                Local processing only
              </Badge>
            </div>
          </div>

          {status !== "done" && (
            <div className="mt-8">
              <div
                {...getRootProps()}
                className={`w-full p-10 border-2 border-dashed rounded-lg text-center cursor-pointer transition-colors ${
                  isDragActive ? "border-flame-red bg-flame-red/10" : "border-flame-red/50 hover:bg-flame-red/5"
                }`}
              >
                <input {...getInputProps()} />
                <FileUp className="mx-auto h-12 w-12 text-flame-red/80" />
                <p className="mt-4 text-body-md text-graphite-60">
                  {isDragActive ? "Drop the files here..." : "Drag 'n' drop some files here, or click to select files"}
                </p>
                <p className="text-caption text-graphite-60/70 mt-1">Maximum 10 photos</p>
              </div>

              {images.length > 0 && (
                <div className="mt-6">
                  <h3 className="font-semibold">Uploaded Photos ({images.length}/10)</h3>
                  <ul className="mt-4 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                    {images.map((image) => (
                      <li key={image.id} className="relative group">
                        <img
                          src={image.preview}
                          alt={image.fileName}
                          className="w-full h-32 object-cover rounded-md"
                        />
                        <button
                          onClick={() => removeImage(image.id)}
                          className="absolute top-1 right-1 bg-black/50 text-white rounded-full p-0.5 opacity-0 group-hover:opacity-100 transition-opacity"
                          disabled={status === "processing"}
                        >
                          <X className="h-4 w-4" />
                        </button>
                      </li>
                    ))}
                  </ul>
                  <Button 
                    size="lg" 
                    onClick={handleAnalyze} 
                    disabled={status === "processing" || !isStorageReady} 
                    className="w-full mt-8"
                  >
                    {status === "processing" ? (
                      <>
                        <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                        Analyzing with AI...
                      </>
                    ) : (
                      `Analyze ${images.length} Photo${images.length !== 1 ? 's' : ''} with AI`
                    )}
                  </Button>
                </div>
              )}
            </div>
          )}

          {status === "processing" && (
            <div className="mt-12 grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {images.map((image) => (
                <AnalyzingCard 
                  key={image.id} 
                  image={image} 
                  progress={progress[image.id]} 
                />
              ))}
            </div>
          )}

          {status === "done" && results.length > 0 && (
            <div className="mt-12">
              <div className="text-center mb-6">
                <h2 className="text-xl font-semibold">Analysis Results</h2>
                <p className="text-graphite-60 mt-2">
                  Your photos have been analyzed using our 5-step AI assessment
                </p>
              </div>
              
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {results.map((result) => (
                  <ScoreCard key={result.fileName} result={result} />
                ))}
              </div>
              
              <div className="mt-8 text-center">
                <Button 
                  onClick={() => {
                    setStatus('idle')
                    setResults([])
                    setProgress({})
                  }}
                  variant="outline"
                  className="mr-4"
                >
                  Analyze More Photos
                </Button>
                <Button asChild size="lg">
                  <Link to="/bio-analyzer">
                    Analyze Bio Next <Sparkles className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
              </div>
              
              <div className="fixed bottom-8 right-8">
                <Button asChild size="lg" className="rounded-full shadow-lg">
                  <Link to="/bio-analyzer">
                    <Sparkles className="h-5 w-5" />
                  </Link>
                </Button>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  )
}