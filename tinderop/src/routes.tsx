import type { RouteObject } from "react-router";
import { Layout } from "./components/Layout";
import { BioAnalyzer } from "./pages/BioAnalyzer";
import { ImageAnalyzer } from "./pages/ImageAnalyzer";
import { LandingPage } from "./pages/LandingPage";

export const routes: RouteObject[] = [
  {
    path: "/",
    element: <Layout />,
    children: [
      {
        index: true,
        element: <LandingPage />,
      },
      {
        path: "image-analyzer",
        element: <ImageAnalyzer />,
      },
      {
        path: "bio-analyzer",
        element: <BioAnalyzer />,
      },
    ],
  },
];
