import { RouteObject } from "react-router";
import { Layout } from "./components/Layout";
import { LandingPage } from "./pages/LandingPage";
import { ImageAnalyzer } from "./pages/ImageAnalyzer";
import { BioAnalyzer } from "./pages/BioAnalyzer";

export const routes: RouteObject[] = [
  {
    path: "/",
    element: <Layout />,
    children: [
      {
        index: true,
        element: <LandingPage />,
      },
      {
        path: "image-analyzer",
        element: <ImageAnalyzer />,
      },
      {
        path: "bio-analyzer", 
        element: <BioAnalyzer />,
      },
    ],
  },
];