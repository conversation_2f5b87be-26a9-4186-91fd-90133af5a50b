"use client";

import { AnimatePresence, motion } from "framer-motion";
import { useEffect, useState } from "react";
import { cn } from "@/lib/utils";

const analysisData = [
  { label: "Confidence", value: 85 },
  { label: "Lighting", value: 78 },
  { label: "Photo Quality", value: 88 },
];

const AnimatedBar = ({ value }: { value: number }) => (
  <div className="w-full bg-gray-200 rounded-full h-4 mt-3">
    <motion.div
      className="bg-gradient-primary h-4 rounded-full"
      initial={{ width: 0 }}
      animate={{ width: `${value}%` }}
      transition={{ duration: 0.88, ease: "easeOut" }}
    />
  </div>
);

export function AnimatedPhoneMockup({
  rotation = 0,
  className,
}: {
  rotation?: number;
  className?: string;
}) {
  const [step, setStep] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setStep((prev) => (prev + 1) % (analysisData.length + 2));
    }, 2200); // 10% slower
    return () => clearInterval(interval);
  }, []);

  const renderStepContent = () => {
    if (step === 0) {
      return (
        <motion.div
          key="profile-bio"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.55 }}
          className="px-4"
        >
          <h3 className="text-xl font-bold">Jessica, 26</h3>
          <p className="text-sm text-graphite-60 mt-1">
            "Lover of dogs, pizza, and spontaneous adventures."
          </p>
        </motion.div>
      );
    }
    if (step > 0 && step <= analysisData.length) {
      const item = analysisData[step - 1];
      return (
        <motion.div
          key={item.label}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.55 }}
          className="px-4 py-2"
        >
          <p className="text-lg font-semibold text-graphite-90 mb-1">{item.label}</p>
          <p className="text-2xl font-bold text-flame-red mb-2">{item.value}%</p>
          <AnimatedBar value={item.value} />
        </motion.div>
      );
    }
    if (step === analysisData.length + 1) {
      return (
        <motion.div
          key="score"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          transition={{ duration: 0.55 }}
          className="px-4 text-center"
        >
          <p className="text-lg font-semibold">Overall Score</p>
          <p className="text-5xl font-bold bg-clip-text text-transparent bg-gradient-primary">86</p>
        </motion.div>
      );
    }
  };

  return (
    <div
      className={cn(
        "absolute h-[600px] w-[300px] rounded-[40px] bg-white shadow-2xl border-4 border-gray-200 overflow-hidden flex flex-col",
        className
      )}
      style={{ transform: `rotate(${rotation}deg)` }}
    >
      <img
        src="/attractive-person-profile.avif"
        alt="Profile"
        className="w-full h-[350px] object-cover object-center flex-shrink-0 bg-gray-100"
      />
      <div className="flex-grow flex items-center justify-center p-4">
        <AnimatePresence mode="wait">{renderStepContent()}</AnimatePresence>
      </div>
    </div>
  );
}
