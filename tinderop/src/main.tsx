import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { createBrowserRouter, RouterProvider } from "react-router";
import { PrivacyManager } from "./components/PrivacyManager";
import { routes } from "./routes";
import "./globals.css";

const router = createBrowserRouter(routes);

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <PrivacyManager>
      <RouterProvider router={router} />
    </PrivacyManager>
  </StrictMode>
);
