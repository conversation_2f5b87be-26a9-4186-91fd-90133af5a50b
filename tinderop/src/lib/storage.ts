// IndexedDB utilities for temporary image storage
const DB_NAME = "TinderOpImageDB";
const DB_VERSION = 1;
const STORE_NAME = "images";
const SESSION_KEY = "tinderop_session";

export interface StoredImage {
  id: string;
  fileName: string;
  blob: Blob;
  mimeType: string;
  uploadedAt: number;
  sessionId: string;
}

export interface AnalysisSession {
  id: string;
  createdAt: number;
  imageIds: string[];
  results: Record<string, any>;
}

class ImageStorage {
  private db: IDBDatabase | null = null;

  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(DB_NAME, DB_VERSION);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        if (!db.objectStoreNames.contains(STORE_NAME)) {
          const store = db.createObjectStore(STORE_NAME, { keyPath: "id" });
          store.createIndex("sessionId", "sessionId", { unique: false });
          store.createIndex("uploadedAt", "uploadedAt", { unique: false });
        }
      };
    });
  }

  async storeImage(file: File, sessionId: string): Promise<string> {
    if (!this.db) await this.init();

    const id = `${sessionId}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const storedImage: StoredImage = {
      id,
      fileName: file.name,
      blob: file,
      mimeType: file.type,
      uploadedAt: Date.now(),
      sessionId,
    };

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([STORE_NAME], "readwrite");
      const store = transaction.objectStore(STORE_NAME);
      const request = store.add(storedImage);

      request.onsuccess = () => resolve(id);
      request.onerror = () => reject(request.error);
    });
  }

  async getImage(id: string): Promise<StoredImage | null> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([STORE_NAME], "readonly");
      const store = transaction.objectStore(STORE_NAME);
      const request = store.get(id);

      request.onsuccess = () => resolve(request.result || null);
      request.onerror = () => reject(request.error);
    });
  }

  async getSessionImages(sessionId: string): Promise<StoredImage[]> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([STORE_NAME], "readonly");
      const store = transaction.objectStore(STORE_NAME);
      const index = store.index("sessionId");
      const request = index.getAll(sessionId);

      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  async deleteImage(id: string): Promise<void> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([STORE_NAME], "readwrite");
      const store = transaction.objectStore(STORE_NAME);
      const request = store.delete(id);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  async clearSession(sessionId: string): Promise<void> {
    if (!this.db) await this.init();

    const images = await this.getSessionImages(sessionId);
    const deletePromises = images.map((img) => this.deleteImage(img.id));
    await Promise.all(deletePromises);
  }

  async cleanup(): Promise<void> {
    if (!this.db) await this.init();

    const cutoff = Date.now() - 24 * 60 * 60 * 1000; // 24 hours ago

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([STORE_NAME], "readwrite");
      const store = transaction.objectStore(STORE_NAME);
      const index = store.index("uploadedAt");
      const request = index.openCursor(IDBKeyRange.upperBound(cutoff));

      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result;
        if (cursor) {
          cursor.delete();
          cursor.continue();
        } else {
          resolve();
        }
      };
      request.onerror = () => reject(request.error);
    });
  }
}

// Session management using localStorage
export class SessionManager {
  private static instance: SessionManager;
  private currentSession: AnalysisSession | null = null;

  static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager();
    }
    return SessionManager.instance;
  }

  getCurrentSession(): AnalysisSession {
    if (this.currentSession) return this.currentSession;

    const stored = localStorage.getItem(SESSION_KEY);
    if (stored) {
      try {
        this.currentSession = JSON.parse(stored);
        return this.currentSession!;
      } catch (e) {
        console.warn("Failed to parse stored session, creating new one");
      }
    }

    this.currentSession = {
      id: `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      createdAt: Date.now(),
      imageIds: [],
      results: {},
    };

    this.saveSession();
    return this.currentSession;
  }

  addImageToSession(imageId: string): void {
    const session = this.getCurrentSession();
    if (!session.imageIds.includes(imageId)) {
      session.imageIds.push(imageId);
      this.saveSession();
    }
  }

  removeImageFromSession(imageId: string): void {
    const session = this.getCurrentSession();
    session.imageIds = session.imageIds.filter((id) => id !== imageId);
    delete session.results[imageId];
    this.saveSession();
  }

  saveAnalysisResult(imageId: string, result: any): void {
    const session = this.getCurrentSession();
    session.results[imageId] = result;
    this.saveSession();
  }

  getAnalysisResult(imageId: string): any {
    const session = this.getCurrentSession();
    return session.results[imageId];
  }

  clearSession(): void {
    localStorage.removeItem(SESSION_KEY);
    this.currentSession = null;
  }

  private saveSession(): void {
    if (this.currentSession) {
      localStorage.setItem(SESSION_KEY, JSON.stringify(this.currentSession));
    }
  }
}

// Global instances
export const imageStorage = new ImageStorage();
export const sessionManager = SessionManager.getInstance();

// Utility functions
export async function convertBlobToBase64(blob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const result = reader.result as string;
      resolve(result.split(",")[1]); // Remove data:image/jpeg;base64, prefix
    };
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
}

export function createImagePreview(blob: Blob): string {
  return URL.createObjectURL(blob);
}

export function revokeImagePreview(url: string): void {
  URL.revokeObjectURL(url);
}

// Initialize cleanup on app start
export async function initStorage(): Promise<void> {
  await imageStorage.init();
  await imageStorage.cleanup(); // Clean up old images
}
