import { openrouter } from "@openrouter/ai-sdk-provider";
import { generateText } from "ai";
import { ANALYSIS_STEPS, type StepResult } from "@/types/analysis";

export class ImageAnalysisAgent {
  private model = openrouter("google/gemini-2.5-flash");

  async analyzeImage(
    imageBase64: string,
    _fileName: string,
    onProgress?: (step: number, stepName: string, progress: number) => void
  ): Promise<StepResult[]> {
    const results: StepResult[] = [];

    for (let i = 0; i < ANALYSIS_STEPS.length; i++) {
      const step = ANALYSIS_STEPS[i];
      onProgress?.(step.id, step.name, (i / ANALYSIS_STEPS.length) * 100);

      const startTime = Date.now();

      try {
        console.log(`🔍 Starting analysis step ${step.id}: ${step.name}`);
        const result = await this.executeAnalysisStep(step.id, step.name, imageBase64);
        const processingTime = Date.now() - startTime;
        console.log(`✅ Completed step ${step.id} in ${processingTime}ms - Score: ${result.score}/100`);

        results.push({
          stepId: step.id,
          stepName: step.name,
          score: result.score,
          insights: result.insights,
          confidence: result.confidence,
          processingTime,
        });

        onProgress?.(step.id, step.name, ((i + 1) / ANALYSIS_STEPS.length) * 100);
      } catch (error) {
        console.error(`❌ Error in step ${step.id} (${step.name}):`, error);
        results.push({
          stepId: step.id,
          stepName: step.name,
          score: 0,
          insights: ["Analysis failed for this step. Please try again."],
          confidence: 0,
          processingTime: Date.now() - startTime,
        });
      }
    }

    return results;
  }

  private async executeAnalysisStep(
    stepId: number,
    _stepName: string,
    imageBase64: string
  ): Promise<{ score: number; insights: string[]; confidence: number }> {
    const prompt = this.getStepPrompt(stepId);

    const { text } = await generateText({
      model: this.model,
      messages: [
        {
          role: "user",
          content: [
            {
              type: "text",
              text: prompt,
            },
            {
              type: "image",
              image: `data:image/jpeg;base64,${imageBase64}`,
            },
          ],
        },
      ],
      maxTokens: 1000,
      temperature: 0.3,
    });

    return this.parseAnalysisResult(text);
  }

  private getStepPrompt(stepId: number): string {
    const baseInstruction = `You are an expert dating profile photo analyst. Analyze this image for step ${stepId} and provide a JSON response with exactly this structure:
{
  "score": <number 0-100>,
  "insights": ["insight1", "insight2", "insight3"],
  "confidence": <number 0-100>
}

Provide 2-4 specific, actionable insights. Be constructive and helpful.`;

    const stepPrompts = {
      1: `${baseInstruction}

STEP 1: TECHNICAL QUALITY ASSESSMENT
Analyze the photo's technical aspects:
- Image resolution and sharpness
- Lighting quality (natural vs artificial, harsh vs soft)
- Composition and framing
- Background quality and distractions
- Color balance and saturation
- Overall photo clarity

Focus on technical photography aspects that affect photo quality.`,

      2: `${baseInstruction}

STEP 2: FACIAL ANALYSIS & ATTRACTIVENESS
Analyze facial features and expressions in detail:
- Facial symmetry and proportions
- Eye contact and gaze direction (direct vs. looking away)
- Smile authenticity and appeal (genuine vs. forced)
- Facial grooming (beard, eyebrows, skin care)
- Facial angle and positioning (profile vs. front-facing)
- Expression and mood conveyed (confident, approachable, mysterious)
- Jawline definition and facial structure
- Hair styling and how it frames the face
- Makeup application (if applicable) and enhancement

Focus on facial aesthetics, expressions, and grooming that enhance dating appeal.`,

      3: `${baseInstruction}

STEP 3: PHYSICAL ANALYSIS & BODY LANGUAGE
Analyze physical presentation and body language comprehensively:

POSTURE & STANCE:
- Body posture (confident, relaxed, tense, slouched)
- Shoulder positioning and alignment
- Spine alignment and overall stance
- Weight distribution and balance

PHYSIQUE & FITNESS:
- Body proportions and symmetry
- Fitness level and muscle definition visibility
- Body composition and athletic appearance
- Physical conditioning indicators
- Overall body shape and silhouette

BODY LANGUAGE & PRESENCE:
- Hand positioning and natural gestures
- Arm placement and comfort level
- Overall body language confidence
- Physical presence and charisma projection
- Energy and vitality conveyed
- Approachability through body language

PRESENTATION FACTORS:
- Height perception and how it's optimized
- Body orientation relative to camera
- Use of space and positioning
- Physical comfort and naturalness
- Overall physical attractiveness and appeal

Provide specific, actionable advice for improving physical presentation and body language for dating success.`,

      4: `${baseInstruction}

STEP 4: STYLE & PRESENTATION ANALYSIS
Analyze clothing, styling, and overall presentation:
- Outfit appropriateness and style
- Color coordination and fit
- Clothing fit and tailoring
- Grooming and personal care
- Setting and environment appropriateness
- Accessory choices and styling
- Overall fashion sense and style personality
- Color choices that complement skin tone
- Seasonal appropriateness of clothing
- Brand perception and style messaging

Focus on style choices that impact dating appeal and personal branding.`,

      5: `${baseInstruction}

STEP 5: DATING PROFILE OPTIMIZATION
Analyze dating-specific factors:
- Photo type suitability (main profile vs additional)
- Uniqueness and memorability
- Conversation starter potential
- Age-appropriate presentation
- Authenticity and genuineness
- Appeal to target demographic

Focus on how this photo performs specifically for dating apps.`,

      6: `${baseInstruction}

STEP 6: OVERALL RECOMMENDATIONS & ACTION PLAN
Synthesize all previous analyses into final recommendations:
- Priority improvement areas
- Specific actionable advice
- Photo ranking suggestions
- Most impactful changes to make
- Overall dating profile strategy
- Timeline for improvements

Focus on creating a comprehensive improvement plan.`,
    };

    return stepPrompts[stepId as keyof typeof stepPrompts] || stepPrompts[1];
  }

  private parseAnalysisResult(response: string): {
    score: number;
    insights: string[];
    confidence: number;
  } {
    try {
      // Extract JSON from response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error("No JSON found in response");
      }

      const parsed = JSON.parse(jsonMatch[0]);

      return {
        score: Math.max(0, Math.min(100, parseInt(parsed.score) || 0)),
        insights: Array.isArray(parsed.insights)
          ? parsed.insights.slice(0, 4).map(String)
          : ["Unable to generate insights for this step"],
        confidence: Math.max(0, Math.min(100, parseInt(parsed.confidence) || 0)),
      };
    } catch (error) {
      console.error("🔧 Failed to parse analysis result:", error);
      console.log("📝 Raw response:", response);
      return {
        score: 50,
        insights: [
          "Analysis completed but results could not be parsed properly",
          "Please try again or check the image quality",
        ],
        confidence: 30,
      };
    }
  }

  calculateOverallScore(results: StepResult[]): number {
    if (results.length === 0) return 0;

    // Weighted scoring - some steps matter more for dating profiles
    const weights = {
      1: 0.12, // Technical Quality
      2: 0.28, // Facial Analysis (most important)
      3: 0.22, // Physical Analysis (very important)
      4: 0.18, // Style & Presentation
      5: 0.18, // Dating Optimization
      6: 0.02, // Final Recommendations
    };

    let weightedSum = 0;
    let totalWeight = 0;

    results.forEach((result) => {
      const weight = weights[result.stepId as keyof typeof weights] || 0.2;
      weightedSum += result.score * weight;
      totalWeight += weight;
    });

    return Math.round(weightedSum / totalWeight);
  }

  generateFinalRecommendations(results: StepResult[]): string[] {
    const recommendations: string[] = [];

    // Analyze scores to prioritize recommendations
    const lowScoreSteps = results.filter((r) => r.score < 60).sort((a, b) => a.score - b.score);
    const mediumScoreSteps = results.filter((r) => r.score >= 60 && r.score < 80);

    if (lowScoreSteps.length > 0) {
      recommendations.push(
        `Priority: Improve ${lowScoreSteps[0].stepName.toLowerCase()} (scored ${lowScoreSteps[0].score}/100)`
      );
    }

    if (mediumScoreSteps.length > 0) {
      recommendations.push(
        `Secondary: Enhance ${mediumScoreSteps[0].stepName.toLowerCase()} for better results`
      );
    }

    const highestStep = results.reduce((max, r) => (r.score > max.score ? r : max), results[0]);
    if (highestStep.score > 80) {
      recommendations.push(
        `Strength: Your ${highestStep.stepName.toLowerCase()} is excellent - use this photo type more`
      );
    }

    // Add general recommendations based on overall score
    const overallScore = this.calculateOverallScore(results);
    if (overallScore < 50) {
      recommendations.push("Consider retaking this photo with better preparation and setup");
    } else if (overallScore < 70) {
      recommendations.push("This photo has potential - focus on the priority improvements above");
    } else {
      recommendations.push("Great photo! Minor tweaks could make it even better");
    }

    return recommendations;
  }
}

export const imageAnalysisAgent = new ImageAnalysisAgent();
