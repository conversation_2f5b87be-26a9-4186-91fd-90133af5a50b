import { openrouter } from '@openrouter/ai-sdk-provider'
import { generateText } from 'ai'
import { StepResult, ANALYSIS_STEPS } from '@/types/analysis'

export class ImageAnalysisAgent {
  private model = openrouter('google/gemini-2.5-flash')

  async analyzeImage(
    imageBase64: string,
    fileName: string,
    onProgress?: (step: number, stepName: string, progress: number) => void
  ): Promise<StepResult[]> {
    const results: StepResult[] = []
    
    for (let i = 0; i < ANALYSIS_STEPS.length; i++) {
      const step = ANALYSIS_STEPS[i]
      onProgress?.(step.id, step.name, (i / ANALYSIS_STEPS.length) * 100)
      
      const startTime = Date.now()
      
      try {
        const result = await this.executeAnalysisStep(step.id, step.name, imageBase64)
        const processingTime = Date.now() - startTime
        
        results.push({
          stepId: step.id,
          stepName: step.name,
          score: result.score,
          insights: result.insights,
          confidence: result.confidence,
          processingTime
        })
        
        onProgress?.(step.id, step.name, ((i + 1) / ANALYSIS_STEPS.length) * 100)
      } catch (error) {
        console.error(`Error in step ${step.id}:`, error)
        results.push({
          stepId: step.id,
          stepName: step.name,
          score: 0,
          insights: ['Analysis failed for this step. Please try again.'],
          confidence: 0,
          processingTime: Date.now() - startTime
        })
      }
    }
    
    return results
  }

  private async executeAnalysisStep(
    stepId: number,
    stepName: string,
    imageBase64: string
  ): Promise<{ score: number; insights: string[]; confidence: number }> {
    const prompt = this.getStepPrompt(stepId)
    
    const { text } = await generateText({
      model: this.model,
      messages: [
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: prompt
            },
            {
              type: 'image',
              image: `data:image/jpeg;base64,${imageBase64}`
            }
          ]
        }
      ],
      maxTokens: 500,
      temperature: 0.3
    })

    return this.parseAnalysisResult(text)
  }

  private getStepPrompt(stepId: number): string {
    const baseInstruction = `You are an expert dating profile photo analyst. Analyze this image for step ${stepId} and provide a JSON response with exactly this structure:
{
  "score": <number 0-100>,
  "insights": ["insight1", "insight2", "insight3"],
  "confidence": <number 0-100>
}

Provide 2-4 specific, actionable insights. Be constructive and helpful.`

    const stepPrompts = {
      1: `${baseInstruction}

STEP 1: TECHNICAL QUALITY ASSESSMENT
Analyze the photo's technical aspects:
- Image resolution and sharpness
- Lighting quality (natural vs artificial, harsh vs soft)
- Composition and framing
- Background quality and distractions
- Color balance and saturation
- Overall photo clarity

Focus on technical photography aspects that affect photo quality.`,

      2: `${baseInstruction}

STEP 2: FACIAL ANALYSIS & ATTRACTIVENESS
Analyze facial features and expressions:
- Facial symmetry and proportions
- Eye contact and gaze direction
- Smile authenticity and appeal
- Grooming and presentation
- Facial angle and positioning
- Expression and mood conveyed

Focus on facial aesthetics and expression appeal for dating profiles.`,

      3: `${baseInstruction}

STEP 3: STYLE & PRESENTATION ANALYSIS
Analyze clothing, styling, and overall presentation:
- Outfit appropriateness and style
- Color coordination and fit
- Grooming and personal care
- Setting and environment appropriateness
- Body language and posture
- Overall styling and fashion sense

Focus on style choices that impact dating appeal.`,

      4: `${baseInstruction}

STEP 4: DATING PROFILE OPTIMIZATION
Analyze dating-specific factors:
- Photo type suitability (main profile vs additional)
- Uniqueness and memorability
- Conversation starter potential
- Age-appropriate presentation
- Authenticity and genuineness
- Appeal to target demographic

Focus on how this photo performs specifically for dating apps.`,

      5: `${baseInstruction}

STEP 5: OVERALL RECOMMENDATIONS & ACTION PLAN
Synthesize all previous analyses into final recommendations:
- Priority improvement areas
- Specific actionable advice
- Photo ranking suggestions
- Most impactful changes to make
- Overall dating profile strategy
- Timeline for improvements

Focus on creating a comprehensive improvement plan.`
    }

    return stepPrompts[stepId as keyof typeof stepPrompts] || stepPrompts[1]
  }

  private parseAnalysisResult(response: string): { score: number; insights: string[]; confidence: number } {
    try {
      // Extract JSON from response
      const jsonMatch = response.match(/\{[\s\S]*\}/)
      if (!jsonMatch) {
        throw new Error('No JSON found in response')
      }

      const parsed = JSON.parse(jsonMatch[0])
      
      return {
        score: Math.max(0, Math.min(100, parseInt(parsed.score) || 0)),
        insights: Array.isArray(parsed.insights) 
          ? parsed.insights.slice(0, 4).map(String)
          : ['Unable to generate insights for this step'],
        confidence: Math.max(0, Math.min(100, parseInt(parsed.confidence) || 0))
      }
    } catch (error) {
      console.error('Failed to parse analysis result:', error)
      return {
        score: 50,
        insights: ['Analysis completed but results could not be parsed properly'],
        confidence: 30
      }
    }
  }

  calculateOverallScore(results: StepResult[]): number {
    if (results.length === 0) return 0
    
    // Weighted scoring - some steps matter more for dating profiles
    const weights = {
      1: 0.15, // Technical Quality
      2: 0.30, // Facial Analysis (most important)
      3: 0.25, // Style & Presentation
      4: 0.25, // Dating Optimization
      5: 0.05  // Final Recommendations
    }
    
    let weightedSum = 0
    let totalWeight = 0
    
    results.forEach(result => {
      const weight = weights[result.stepId as keyof typeof weights] || 0.2
      weightedSum += result.score * weight
      totalWeight += weight
    })
    
    return Math.round(weightedSum / totalWeight)
  }

  generateFinalRecommendations(results: StepResult[]): string[] {
    const recommendations: string[] = []
    
    // Analyze scores to prioritize recommendations
    const lowScoreSteps = results.filter(r => r.score < 60).sort((a, b) => a.score - b.score)
    const mediumScoreSteps = results.filter(r => r.score >= 60 && r.score < 80)
    
    if (lowScoreSteps.length > 0) {
      recommendations.push(`Priority: Improve ${lowScoreSteps[0].stepName.toLowerCase()} (scored ${lowScoreSteps[0].score}/100)`)
    }
    
    if (mediumScoreSteps.length > 0) {
      recommendations.push(`Secondary: Enhance ${mediumScoreSteps[0].stepName.toLowerCase()} for better results`)
    }
    
    const highestStep = results.reduce((max, r) => r.score > max.score ? r : max, results[0])
    if (highestStep.score > 80) {
      recommendations.push(`Strength: Your ${highestStep.stepName.toLowerCase()} is excellent - use this photo type more`)
    }
    
    // Add general recommendations based on overall score
    const overallScore = this.calculateOverallScore(results)
    if (overallScore < 50) {
      recommendations.push('Consider retaking this photo with better preparation and setup')
    } else if (overallScore < 70) {
      recommendations.push('This photo has potential - focus on the priority improvements above')
    } else {
      recommendations.push('Great photo! Minor tweaks could make it even better')
    }
    
    return recommendations
  }
}

export const imageAnalysisAgent = new ImageAnalysisAgent()