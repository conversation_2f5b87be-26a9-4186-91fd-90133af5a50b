{"$schema": "https://biomejs.dev/schemas/2.0.6/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false, "includes": ["src/**/*.{js,jsx,ts,tsx}", "*.{js,jsx,ts,tsx}", "**/*.{js,jsx,ts,tsx}"]}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "lineWidth": 100, "lineEnding": "lf"}, "linter": {"enabled": true, "rules": {"recommended": true, "complexity": {"noExcessiveCognitiveComplexity": "warn", "noVoid": "off"}, "correctness": {"noUnusedVariables": "error", "useExhaustiveDependencies": "warn"}, "style": {"noNonNullAssertion": "warn", "useImportType": "error", "useNodejsImportProtocol": "off"}, "suspicious": {"noExplicitAny": "warn", "noArrayIndexKey": "warn"}}}, "javascript": {"formatter": {"quoteStyle": "double", "jsxQuoteStyle": "double", "semicolons": "always", "trailingCommas": "es5"}}, "json": {"formatter": {"indentStyle": "space", "indentWidth": 2}}, "assist": {"enabled": true, "actions": {"source": {"organizeImports": "on"}}}}