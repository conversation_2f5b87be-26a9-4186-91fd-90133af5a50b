# Biome Configuration for TinderOP

This project uses [Biome](https://biomejs.dev/) for fast linting and formatting of TypeScript/React code.

## Quick Start

```bash
# Check for issues
bun run lint

# Fix auto-fixable issues
bun run lint:fix

# Format code
bun run format

# Verbose checking with detailed output
bun run check

# Verbose fixing with detailed output
bun run check:fix
```

## VS Code Integration

1. Install the [Biome VS Code extension](https://marketplace.visualstudio.com/items?itemName=biomejs.biome)
2. The project includes VS Code settings that will:
   - Use Biome as the default formatter
   - Format on save
   - Organize imports automatically
   - Show linting errors in real-time

## Configuration

The Biome configuration is in `biome.json` and includes:

- **Formatting**: 2-space indentation, double quotes, semicolons
- **Import Organization**: Automatic import sorting and organization
- **Linting Rules**: TypeScript/React-specific rules with performance optimizations
- **File Inclusion**: Processes all `.js`, `.jsx`, `.ts`, `.tsx` files in `src/`

## Benefits

- ⚡ **Fast**: Written in Rust, significantly faster than ESLint/Prettier
- 🔧 **Unified**: Single tool for both linting and formatting
- 🎯 **Zero Config**: Works out of the box with sensible defaults
- 📦 **TypeScript Native**: Built-in TypeScript support without plugins
- 🔄 **Import Sorting**: Automatic import organization

## Current Status

- ✅ 68 files automatically formatted and fixed
- ✅ Import organization applied to all files
- ✅ Consistent code style across the project
- ⚠️ Some remaining accessibility and unused import warnings (non-breaking)

## Commands Reference

| Command | Description |
|---------|-------------|
| `bun run lint` | Check for linting issues |
| `bun run lint:fix` | Fix auto-fixable linting issues |
| `bun run format` | Format code according to style rules |
| `bun run check` | Verbose check with detailed output |
| `bun run check:fix` | Verbose fix with detailed output |

## Integration with Build Process

Biome is integrated into the development workflow but doesn't interfere with the build process. The existing Vite build and TypeScript checking continue to work as before.

For CI/CD integration, you can add:
```bash
bun run check  # Fails if there are unfixed issues
```
